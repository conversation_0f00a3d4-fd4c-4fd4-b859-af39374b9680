# 多语言宠物博客站群系统 - 详细需求文档

## 📋 项目概述

### 项目名称
多语言宠物博客站群管理系统

### 项目目标
构建一个专注于宠物（猫狗）知识分享的多语言博客站群系统，每个域名对应一种语言，通过统一后台管理多个独立的语言站点，实现内容的AI翻译和人工校对发布流程。

### 核心特点
- **多语言独立模板**：每种语言使用独立的前端模板，非i18n方案
- **域名语言绑定**：每个顶级域名对应一种语言
- **AI翻译工作流**：中文原文 → AI翻译 → 人工校对 → 发布
- **SEO优化**：严格按照Google SEO最佳实践设计
- **统一后台管理**：一个后台管理所有语言站点

## 🏗️ 技术架构

### 前端技术栈
- **框架**：Astro（静态站点生成，SEO友好）
- **样式**：Tailwind CSS
- **构建工具**：Vite
- **部署**：静态文件部署

### 后端技术栈
- **运行环境**：Node.js
- **Web框架**：Express.js
- **数据库**：MySQL 9.0.1
- **缓存**：Redis
- **文件存储**：本地服务器存储

### 部署环境
- **服务器**：Linux VPS + 宝塔面板
- **开发环境**：macOS本地开发
- **数据库**：远程MySQL服务器

## 🎯 目标市场与语言

### 初期覆盖国家/语言
1. **美国** - 英语模板
2. **德国** - 德语模板  
3. **俄罗斯** - 俄语模板

### 扩展性要求
- 支持后期添加更多国家和语言
- 新语言添加流程：复制英文模板 → 修改语言内容 → 后端接入

## 📱 前端页面设计

### 必需页面列表

#### 1. 首页 (/)
**功能要求：**
- 展示最新文章列表
- 热门文章推荐
- 分类导航
- 搜索功能入口
- SEO优化的页面结构

**与后端交互：**
- 获取最新文章列表API
- 获取热门文章API
- 获取分类列表API

#### 2. 文章详情页 (/article/[slug])
**功能要求：**
- 文章完整内容展示
- 面包屑导航
- 相关文章推荐
- 评论系统（多层嵌套）
- 社交分享按钮
- 结构化数据标记

**与后端交互：**
- 获取文章详情API
- 获取相关文章API
- 评论提交API
- 获取评论列表API

#### 3. 分类页面 (/category/[category-slug])
**功能要求：**
- 分类文章列表
- 分页功能
- 分类描述
- 面包屑导航

**与后端交互：**
- 获取分类文章列表API
- 获取分类信息API

#### 4. 搜索结果页 (/search)
**功能要求：**
- 搜索结果列表
- 搜索关键词高亮
- 分页功能
- 无结果状态页面

**与后端交互：**
- 文章搜索API

#### 5. 关于我们页面 (/about)
**功能要求：**
- 网站介绍
- 静态内容页面

#### 6. 隐私政策页面 (/privacy)
**功能要求：**
- 隐私政策内容
- 法律合规要求

### 评论系统设计
**功能特点：**
- 多层嵌套评论（支持回复）
- 用户信息：邮箱 + 用户名（必填）
- 后台审核机制
- 无需用户注册登录
- 无需头像上传

**评论表单字段：**
- 用户名（必填）
- 邮箱（必填）
- 评论内容（必填）
- 父评论ID（回复时）

## 🗂️ 内容分类设计

### 宠物分类结构（两级分类）

#### 一级分类：
1. **猫咪知识** (cats)
2. **狗狗知识** (dogs)

#### 二级分类：
**猫咪知识下：**
- 猫咪健康 (cat-health)
- 猫咪行为 (cat-behavior)  
- 猫咪品种 (cat-breeds)
- 猫咪护理 (cat-care)

**狗狗知识下：**
- 狗狗健康 (dog-health)
- 狗狗训练 (dog-training)
- 狗狗品种 (dog-breeds)
- 狗狗护理 (dog-care)

### 多语言分类名称
每个语言站点的分类名称需要本地化翻译，不能出现俄语网站显示英语分类名称的情况。

## 🔧 后台管理系统

### 管理员权限
- 单一管理员账户
- 管理所有语言站点
- 统一的管理界面

### 核心功能模块

#### 1. 站点管理
**功能：**
- 域名与语言绑定设置
- 站点基本信息配置
- 语言模板管理

#### 2. 内容管理
**功能：**
- 文章编辑器（支持图片粘贴）
- 文章翻译工作流
- 草稿管理
- 文章发布管理
- 分类管理

**翻译工作流：**
1. 创建中文原始文章
2. 点击翻译按钮
3. AI自动翻译到各语言
4. 保存到草稿状态
5. 人工校对编辑
6. 校对完成后发布

#### 3. 评论管理
**功能：**
- 评论审核
- 评论回复
- 评论删除
- 垃圾评论过滤

#### 4. AI翻译设置
**功能：**
- OpenAI API配置
- 翻译模型选择
- 翻译质量设置

#### 5. 广告管理
**功能：**
- Google Ads代码设置
- 每个语言站点独立广告配置
- 广告开关控制
- 广告位置管理

#### 6. 统计代码管理
**功能：**
- Google Analytics代码设置
- 每个语言站点独立统计配置
- 统计代码开关控制

#### 7. SEO设置
**功能：**
- 元数据管理（标题、描述、关键词）
- 结构化数据配置
- 网站地图自动生成
- URL结构设置

## 🔍 SEO优化策略

### URL结构设计
**最佳实践要求：**
- 使用本地化语言的URL Slug
- 简洁明了的URL结构
- 包含关键词的URL
- 避免动态参数

**URL示例：**
```
英语站点：
- 首页：https://example.com/
- 分类：https://example.com/category/cat-health/
- 文章：https://example.com/article/how-to-care-for-your-cat/

德语站点：
- 首页：https://example-de.com/
- 分类：https://example-de.com/kategorie/katzen-gesundheit/
- 文章：https://example-de.com/artikel/wie-man-sich-um-seine-katze-kuemmert/
```

### 元数据管理
**每篇文章必须包含：**
- 标题标签（Title Tag）
- 描述标签（Meta Description）
- 关键词标签（Meta Keywords）
- Open Graph标签
- Twitter Card标签

### 结构化数据
**实现要求：**
- Article Schema
- BreadcrumbList Schema
- Organization Schema
- WebSite Schema

### 网站地图
**自动生成：**
- XML Sitemap
- 分类页面地图
- 文章页面地图
- 多语言站点地图

## 💾 数据库设计要点

### 数据隔离原则
每个语言站点的数据完全独立，包括：
- 文章数据
- 分类数据
- 评论数据
- 用户数据

### 核心数据表

#### 1. 站点配置表 (sites)
- 站点ID
- 域名
- 语言代码
- 站点名称
- 站点描述
- 广告设置
- 统计代码
- SEO设置

#### 2. 文章表 (articles)
- 文章ID
- 站点ID（关联站点）
- 标题
- 内容
- 摘要
- URL Slug
- 分类ID
- 发布状态
- SEO元数据
- 创建时间
- 更新时间

#### 3. 分类表 (categories)
- 分类ID
- 站点ID（关联站点）
- 分类名称
- 分类描述
- URL Slug
- 父分类ID
- 排序

#### 4. 评论表 (comments)
- 评论ID
- 文章ID
- 站点ID（关联站点）
- 用户名
- 邮箱
- 评论内容
- 父评论ID
- 审核状态
- IP地址
- 创建时间

#### 5. 翻译记录表 (translations)
- 翻译ID
- 原文文章ID
- 目标语言
- 翻译状态
- 翻译内容
- 校对状态

## 🚀 开发与部署流程

### 本地开发环境
**域名测试解决方案：**
- 使用hosts文件配置本地域名
- 配置示例：
```
127.0.0.1 local-en.test
127.0.0.1 local-de.test  
127.0.0.1 local-ru.test
```

### 部署流程
1. **代码部署**：上传到VPS服务器
2. **数据库配置**：连接远程MySQL
3. **域名解析**：配置DNS指向服务器
4. **SSL证书**：配置HTTPS
5. **宝塔面板**：配置虚拟主机

### 图片存储策略
- 本地服务器存储
- 统一的图片管理接口
- 图片压缩优化
- CDN加速（可选）

## 📊 性能与扩展性

### 缓存策略
- Redis缓存热门内容
- 静态文件缓存
- 数据库查询缓存

### 预期访问量
- 前期：1万PV/日
- 后期：可扩展到更高访问量

### 扩展性设计
- 支持添加新语言模板
- 支持添加新功能模块
- 数据库水平扩展能力

## 🔐 安全与合规

### 数据安全
- 用户数据加密存储
- SQL注入防护
- XSS攻击防护

### 法律合规
- GDPR合规（欧洲用户）
- 隐私政策页面
- Cookie使用声明

## 📈 运营支持

### 内容发布计划
- 每日发布5篇文章
- 无现有文章迁移需求
- 专注原创内容

### 广告策略
- Google Ads集成
- 不影响用户体验的广告位置
- 每个站点独立广告配置
- 广告开关控制

### 统计分析
- Google Analytics集成
- 每个站点独立统计
- 访问数据分析
- SEO效果监控

---

## 📝 开发优先级

### 第一阶段：核心功能
1. 基础架构搭建
2. 数据库设计实现
3. 后台管理系统
4. 英语模板开发

### 第二阶段：多语言支持
1. 德语模板开发
2. 俄语模板开发
3. AI翻译功能
4. 域名识别系统

### 第三阶段：优化完善
1. SEO优化
2. 性能优化
3. 安全加固
4. 功能完善

## 🔧 技术实现细节

### 域名识别机制
**实现方案：**
- 通过HTTP请求的Host头识别域名
- 后端中间件解析域名并匹配对应语言
- 动态加载对应语言的模板和数据

**代码逻辑：**
```javascript
// 域名 -> 语言映射
const domainLanguageMap = {
  'example.com': 'en',
  'example-de.com': 'de',
  'example-ru.com': 'ru'
};

// 中间件识别语言
app.use((req, res, next) => {
  const host = req.get('host');
  const language = domainLanguageMap[host] || 'en';
  req.language = language;
  next();
});
```

### 模板扩展方案
**新语言添加流程：**
1. 复制英文模板目录
2. 重命名为新语言代码（如：fr, es, it）
3. 翻译模板中的静态文本
4. 更新后端域名语言映射
5. 配置数据库语言站点

**目录结构示例：**
```
templates/
├── en/          # 英语模板
├── de/          # 德语模板
├── ru/          # 俄语模板
└── [new-lang]/  # 新语言模板
```

### 图片处理方案
**上传流程：**
1. 编辑器粘贴图片
2. 前端转换为Base64
3. 后端接收并保存到本地
4. 返回图片访问URL
5. 编辑器插入图片链接

**存储结构：**
```
uploads/
├── 2024/
│   ├── 01/
│   │   ├── article-images/
│   │   └── thumbnails/
│   └── 02/
└── 2025/
```

### Redis缓存策略
**缓存内容：**
- 热门文章列表（1小时）
- 分类文章列表（30分钟）
- 网站配置信息（24小时）
- 搜索结果（15分钟）

**缓存键命名：**
```
site:{language}:articles:hot
site:{language}:category:{id}:articles
site:{language}:config
search:{language}:{keyword}
```

## 🎨 前端设计规范

### 响应式设计
**断点设置：**
- 移动端：< 768px
- 平板端：768px - 1024px
- 桌面端：> 1024px

### 色彩方案
**建议配色：**
- 主色调：温暖的橙色系（宠物友好）
- 辅助色：柔和的蓝色和绿色
- 文字色：深灰色（#333333）
- 背景色：纯白色（#FFFFFF）

### 字体选择
**多语言字体支持：**
- 英语：Inter, Roboto
- 德语：Inter, Roboto
- 俄语：Inter, Roboto, "PT Sans"
- 中文：PingFang SC, "Microsoft YaHei"

### 广告位置设计
**推荐位置：**
1. 文章内容中间（不影响阅读）
2. 侧边栏顶部
3. 文章底部
4. 分类页面顶部

**广告尺寸：**
- 横幅广告：728x90, 320x50
- 矩形广告：300x250, 336x280
- 侧边栏：160x600, 300x600

## 📱 移动端优化

### 性能优化
- 图片懒加载
- 代码分割
- 资源压缩
- CDN加速

### 用户体验
- 触摸友好的按钮尺寸
- 快速加载的页面
- 简洁的导航结构
- 易读的字体大小

## 🔍 SEO技术实现

### 结构化数据示例
```json
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "如何照顾你的猫咪",
  "author": {
    "@type": "Organization",
    "name": "宠物知识网"
  },
  "datePublished": "2024-01-15",
  "image": "https://example.com/cat-care.jpg"
}
```

### 网站地图生成
**自动生成逻辑：**
- 每次发布文章时更新sitemap
- 包含所有已发布文章
- 按语言分别生成
- 提交到Google Search Console

### 页面加载优化
- 关键CSS内联
- 非关键CSS异步加载
- JavaScript延迟加载
- 图片格式优化（WebP）

## 🛡️ 安全措施

### 输入验证
- 评论内容XSS过滤
- SQL注入防护
- 文件上传类型限制
- 表单CSRF保护

### 数据保护
- 敏感信息加密存储
- 定期数据备份
- 访问日志记录
- 异常监控告警

## 📊 监控与分析

### 性能监控
- 页面加载时间
- 数据库查询性能
- 服务器资源使用
- 错误日志监控

### 业务指标
- 文章浏览量
- 用户停留时间
- 评论互动率
- 搜索关键词排名

## 🔄 维护与更新

### 内容维护
- 定期检查链接有效性
- 更新过时内容
- 优化SEO表现
- 监控评论质量

### 技术维护
- 定期安全更新
- 性能优化调整
- 备份策略执行
- 监控系统健康

---

## 📋 开发检查清单

### 前端开发
- [ ] Astro项目初始化
- [ ] 多语言模板结构
- [ ] 响应式设计实现
- [ ] SEO优化配置
- [ ] 评论系统前端
- [ ] 搜索功能实现

### 后端开发
- [ ] Express.js API服务
- [ ] 数据库设计实现
- [ ] 域名识别中间件
- [ ] AI翻译接口集成
- [ ] 文件上传处理
- [ ] Redis缓存配置

### 管理后台
- [ ] 管理员认证系统
- [ ] 文章编辑器
- [ ] 翻译工作流
- [ ] 评论审核系统
- [ ] 广告管理界面
- [ ] 站点配置管理

### 部署配置
- [ ] 服务器环境配置
- [ ] 数据库连接配置
- [ ] 域名DNS配置
- [ ] SSL证书配置
- [ ] 宝塔面板配置

---

**本需求文档涵盖了项目的所有核心功能和技术细节，为开发团队提供了完整的实施指南。所有功能将按照此文档严格执行开发。**
